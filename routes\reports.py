from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, make_response, current_app
from flask_login import login_required, current_user
from datetime import datetime, date, timedelta

bp = Blueprint('reports', __name__, url_prefix='/reports')

def get_models():
    """الحصول على النماذج من التطبيق الحالي"""
    try:
        from flask import current_app
        return current_app.config.get('MODELS', {})
    except RuntimeError:
        # في حالة عدم وجود app context
        return {}

@bp.route('/')
@login_required
def index():
    """صفحة التقارير الرئيسية"""
    try:
        return render('reports/index.html')
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/cases')
@login_required
def cases_report():
    """تقرير القضايا"""
    # فلترة التواريخ
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    case_status = request.GET.get('case_status', '')
    lawyer_id = request.GET.get('lawyer_id', '')
    
    query = Case.query.filter_by(is_active=True)
    
    if start_date:
        query = query.filter(Case.created_at >= datetime.strptime(start_date, '%Y-%m-%d'))
    
    if end_date:
        query = query.filter(Case.created_at <= datetime.strptime(end_date, '%Y-%m-%d'))
    
    if case_status:
        query = query.filter_by(case_status=case_status)
    
    if lawyer_id:
        query = query.filter_by(lawyer_id=lawyer_id)
    
    cases = query.all()
    
    # إحصائيات
    total_cases = len(cases)
    status_stats = {}
    for case in cases:
        status_stats[case.case_status] = status_stats.get(case.case_status, 0) + 1
    
    lawyers = Lawyer.query.filter_by(is_active=True).all()
    
    return render('reports/cases.html', 
                         cases=cases,
                         total_cases=total_cases,
                         status_stats=status_stats,
                         lawyers=lawyers,
                         start_date=start_date,
                         end_date=end_date,
                         case_status=case_status,
                         lawyer_id=lawyer_id)

@bp.route('/financial')
@login_required
def financial_report():
    """التقرير المالي"""
    # فلترة التواريخ
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    
    # استعلام الفواتير
    invoice_query = Invoice.query
    payment_query = Payment.query
    
    if start_date:
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        invoice_query = invoice_query.filter(Invoice.issue_date >= start_dt.date())
        payment_query = payment_query.filter(Payment.payment_date >= start_dt.date())
    
    if end_date:
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        invoice_query = invoice_query.filter(Invoice.issue_date <= end_dt.date())
        payment_query = payment_query.filter(Payment.payment_date <= end_dt.date())
    
    invoices = invoice_query.all()
    payments = payment_query.filter_by(payment_status='completed').all()
    
    # حسابات مالية
    total_invoiced = sum(invoice.total_amount for invoice in invoices)
    total_paid = sum(payment.amount for payment in payments)
    total_outstanding = total_invoiced - total_paid
    
    # إحصائيات حالة الفواتير
    invoice_status_stats = {}
    for invoice in invoices:
        invoice_status_stats[invoice.invoice_status] = invoice_status_stats.get(invoice.invoice_status, 0) + 1
    
    # المدفوعات الشهرية
    monthly_payments = db.query(
        extract('year', Payment.payment_date).label('year'),
        extract('month', Payment.payment_date).label('month'),
        func.sum(Payment.amount).label('total')
    ).filter_by(payment_status='completed').group_by(
        extract('year', Payment.payment_date),
        extract('month', Payment.payment_date)
    ).all()
    
    return render('reports/financial.html',
                         invoices=invoices,
                         payments=payments,
                         total_invoiced=total_invoiced,
                         total_paid=total_paid,
                         total_outstanding=total_outstanding,
                         invoice_status_stats=invoice_status_stats,
                         monthly_payments=monthly_payments,
                         start_date=start_date,
                         end_date=end_date)

@bp.route('/lawyers_performance')
@login_required
def lawyers_performance():
    """تقرير أداء المحامين"""
    lawyers = Lawyer.query.filter_by(is_active=True).all()
    
    performance_data = []
    for lawyer in lawyers:
        cases = Case.query.filter_by(lawyer_id=lawyer.id, is_active=True).all()
        
        total_cases = len(cases)
        active_cases = len([c for c in cases if c.case_status in ['pending', 'active']])
        won_cases = len([c for c in cases if c.case_status == 'won'])
        lost_cases = len([c for c in cases if c.case_status == 'lost'])
        
        # حساب الإيرادات
        invoices = db.query(func.sum(Invoice.total_amount)).join(Case).filter(
            Case.lawyer_id == lawyer.id,
            Invoice.invoice_status == 'paid'
        ).scalar() or 0
        
        win_rate = (won_cases / total_cases * 100) if total_cases > 0 else 0
        
        performance_data.append({
            'lawyer': lawyer,
            'total_cases': total_cases,
            'active_cases': active_cases,
            'won_cases': won_cases,
            'lost_cases': lost_cases,
            'win_rate': round(win_rate, 2),
            'revenue': invoices
        })
    
    return render('reports/lawyers_performance.html', performance_data=performance_data)

@bp.route('/clients')
@login_required
def clients_report():
    """تقرير العملاء"""
    clients = Client.query.filter_by(is_active=True).all()
    
    client_data = []
    for client in clients:
        cases = Case.query.filter_by(client_id=client.id, is_active=True).all()
        invoices = Invoice.query.filter_by(client_id=client.id).all()
        
        total_cases = len(cases)
        active_cases = len([c for c in cases if c.case_status in ['pending', 'active']])
        total_invoiced = sum(inv.total_amount for inv in invoices)
        total_paid = sum(
            sum(p.amount for p in inv.payments if p.payment_status == 'completed') 
            for inv in invoices
        )
        outstanding = total_invoiced - total_paid
        
        client_data.append({
            'client': client,
            'total_cases': total_cases,
            'active_cases': active_cases,
            'total_invoiced': total_invoiced,
            'total_paid': total_paid,
            'outstanding': outstanding
        })
    
    return render('reports/clients.html', client_data=client_data)

@bp.route('/dashboard_stats')
@login_required
def dashboard_stats():
    """إحصائيات لوحة التحكم"""
    today = date.today()
    this_month = today.replace(day=1)
    last_month = (this_month - timedelta(days=1)).replace(day=1)
    
    # إحصائيات عامة
    total_cases = Case.query.filter_by(is_active=True).count()
    total_clients = Client.query.filter_by(is_active=True).count()
    total_lawyers = Lawyer.query.filter_by(is_active=True).count()
    
    # إحصائيات هذا الشهر
    cases_this_month = Case.query.filter(Case.created_at >= this_month).count()
    revenue_this_month = db.query(func.sum(Payment.amount)).filter(
        Payment.payment_date >= this_month,
        Payment.payment_status == 'completed'
    ).scalar() or 0
    
    # المواعيد القادمة
    upcoming_appointments = Appointment.query.filter(
        Appointment.appointment_date >= datetime.now(),
        Appointment.status == 'scheduled'
    ).count()
    
    # الفواتير المتأخرة
    overdue_invoices = Invoice.query.filter(
        Invoice.due_date < today,
        Invoice.invoice_status.in_(['pending', 'overdue'])
    ).count()
    
    stats = {
        'total_cases': total_cases,
        'total_clients': total_clients,
        'total_lawyers': total_lawyers,
        'cases_this_month': cases_this_month,
        'revenue_this_month': float(revenue_this_month),
        'upcoming_appointments': upcoming_appointments,
        'overdue_invoices': overdue_invoices
    }
    
    return JsonResponse(stats)
