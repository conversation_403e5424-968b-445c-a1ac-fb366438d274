from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime, date

bp = Blueprint('invoices', __name__, url_prefix='/invoices')

def get_models():
    """الحصول على النماذج من التطبيق الحالي"""
    try:
        from flask import current_app
        return current_app.config.get('MODELS', {})
    except RuntimeError:
        # في حالة عدم وجود app context
        return {}

@bp.route('/')
@login_required
def index():
    """عرض جميع الفواتير"""
    try:
        return render('invoices/index.html')
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    """إضافة فاتورة جديدة"""
    if request.method == 'POST':
        try:
            # إنشاء رقم فاتورة فريد
            invoice_number = f"INV-{datetime.now().year}-{str(uuid.uuid4())[:8].upper()}"
            
            amount = float(request.POST['amount'])
            tax_rate = float(request.POST.get('tax_rate', 0)) / 100
            tax_amount = amount * tax_rate
            total_amount = amount + tax_amount
            
            invoice = Invoice(
                invoice_number=invoice_number,
                client_id=request.POST['client_id'],
                case_id=request.POST.get('case_id') if request.POST.get('case_id') else None,
                amount=amount,
                tax_amount=tax_amount,
                total_amount=total_amount,
                issue_date=datetime.strptime(request.POST['issue_date'], '%Y-%m-%d').date(),
                due_date=datetime.strptime(request.POST['due_date'], '%Y-%m-%d').date(),
                description=request.POST.get('description')
            )
            
            db.add(invoice)
            db.save()
            
            flash('تم إضافة الفاتورة بنجاح', 'success')
            return redirect(url_for('invoices.view', id=invoice.id))
            
        except Exception as e:
            db.delete()
            flash(f'حدث خطأ أثناء إضافة الفاتورة: {str(e)}', 'error')
    
    clients = Client.query.filter_by(is_active=True).all()
    
    return render('invoices/add.html', clients=clients)

@bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل الفاتورة"""
    invoice = Invoice.query.get_or_404(id)
    payments = Payment.query.filter_by(invoice_id=id).order_by(Payment.payment_date.desc()).all()
    
    # حساب المبلغ المدفوع والمتبقي
    total_paid = sum(payment.amount for payment in payments if payment.payment_status == 'completed')
    remaining_amount = invoice.total_amount - total_paid
    
    return render('invoices/view.html', 
                         invoice=invoice, 
                         payments=payments,
                         total_paid=total_paid,
                         remaining_amount=remaining_amount)

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """تعديل الفاتورة"""
    invoice = Invoice.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            amount = float(request.POST['amount'])
            tax_rate = float(request.POST.get('tax_rate', 0)) / 100
            tax_amount = amount * tax_rate
            total_amount = amount + tax_amount
            
            invoice.client_id = request.POST['client_id']
            invoice.case_id = request.POST.get('case_id') if request.POST.get('case_id') else None
            invoice.amount = amount
            invoice.tax_amount = tax_amount
            invoice.total_amount = total_amount
            invoice.invoice_status = request.POST['invoice_status']
            invoice.issue_date = datetime.strptime(request.POST['issue_date'], '%Y-%m-%d').date()
            invoice.due_date = datetime.strptime(request.POST['due_date'], '%Y-%m-%d').date()
            invoice.description = request.POST.get('description')
            invoice.updated_at = datetime.utcnow()
            
            db.save()
            
            flash('تم تحديث الفاتورة بنجاح', 'success')
            return redirect(url_for('invoices.view', id=invoice.id))
            
        except Exception as e:
            db.delete()
            flash(f'حدث خطأ أثناء تحديث الفاتورة: {str(e)}', 'error')
    
    clients = Client.query.filter_by(is_active=True).all()
    cases = Case.query.filter_by(client_id=invoice.client_id, is_active=True).all()
    
    return render('invoices/edit.html', invoice=invoice, clients=clients, cases=cases)

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """حذف الفاتورة"""
    invoice = Invoice.query.get_or_404(id)
    
    # التحقق من وجود مدفوعات
    payments_count = Payment.query.filter_by(invoice_id=id).count()
    
    if payments_count > 0:
        flash('لا يمكن حذف الفاتورة لوجود مدفوعات مرتبطة بها', 'error')
        return redirect(url_for('invoices.view', id=id))
    
    try:
        db.delete(invoice)
        db.save()
        
        flash('تم حذف الفاتورة بنجاح', 'success')
        
    except Exception as e:
        db.delete()
        flash(f'حدث خطأ أثناء حذف الفاتورة: {str(e)}', 'error')
    
    return redirect(url_for('invoices.index'))

@bp.route('/<int:id>/add_payment', methods=['POST'])
@login_required
def add_payment(id):
    """إضافة دفعة للفاتورة"""
    invoice = Invoice.query.get_or_404(id)
    
    try:
        payment = Payment(
            invoice_id=id,
            amount=float(request.POST['amount']),
            payment_method=request.POST['payment_method'],
            payment_date=datetime.strptime(request.POST['payment_date'], '%Y-%m-%d').date(),
            reference_number=request.POST.get('reference_number'),
            notes=request.POST.get('notes')
        )
        
        db.add(payment)
        
        # تحديث حالة الفاتورة إذا تم دفع المبلغ كاملاً
        total_payments = db.query(db.func.sum(Payment.amount)).filter_by(
            invoice_id=id, payment_status='completed'
        ).scalar() or 0
        
        total_payments += payment.amount
        
        if total_payments >= invoice.total_amount:
            invoice.invoice_status = 'paid'
            invoice.payment_date = payment.payment_date
        
        db.save()
        
        flash('تم إضافة الدفعة بنجاح', 'success')
        
    except Exception as e:
        db.delete()
        flash(f'حدث خطأ أثناء إضافة الدفعة: {str(e)}', 'error')
    
    return redirect(url_for('invoices.view', id=id))

@bp.route('/overdue')
@login_required
def overdue():
    """الفواتير المتأخرة"""
    today = date.today()
    overdue_invoices = Invoice.query.filter(
        Invoice.due_date < today,
        Invoice.invoice_status.in_(['pending', 'overdue'])
    ).order_by(Invoice.due_date).all()
    
    return render('invoices/overdue.html', invoices=overdue_invoices)

@bp.route('/api/client_cases/<int:client_id>')
@login_required
def api_client_cases(client_id):
    """الحصول على قضايا العميل عبر API"""
    cases = Case.query.filter_by(client_id=client_id, is_active=True).all()
    
    results = []
    for case in cases:
        results.append({
            'id': case.id,
            'text': f"{case.case_number} - {case.case_title}"
        })
    
    return JsonResponse(results)
