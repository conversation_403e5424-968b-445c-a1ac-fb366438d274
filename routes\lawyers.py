from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime

bp = Blueprint('lawyers', __name__, url_prefix='/lawyers')

def get_models():
    """الحصول على النماذج من التطبيق الحالي"""
    try:
        from flask import current_app
        return current_app.config.get('MODELS', {})
    except RuntimeError:
        # في حالة عدم وجود app context
        return {}

@bp.route('/')
@login_required
def index():
    """عرض جميع المحامين"""
    try:
        return render('lawyers/index.html',
                             search=request.GET.get('search', ''))
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    """إضافة محامي جديد"""
    if request.method == 'POST':
        try:
            # إنشاء حساب مستخدم أولاً
            user = User(
                username=request.POST['username'],
                email=request.POST['email'],
                password_hash=generate_password_hash(request.POST['password']),
                full_name=request.POST['full_name'],
                phone=request.POST.get('phone'),
                role='lawyer'
            )
            
            db.add(user)
            db.flush()  # للحصول على user.id
            
            # إنشاء ملف المحامي
            lawyer = Lawyer(
                user_id=user.id,
                license_number=request.POST['license_number'],
                specialization=request.POST.get('specialization'),
                hourly_rate=request.POST.get('hourly_rate') or 0,
                bio=request.POST.get('bio')
            )
            
            db.add(lawyer)
            db.save()
            
            flash('تم إضافة المحامي بنجاح', 'success')
            return redirect(url_for('lawyers.view', id=lawyer.id))
            
        except Exception as e:
            db.delete()
            flash(f'حدث خطأ أثناء إضافة المحامي: {str(e)}', 'error')
    
    return render('lawyers/add.html')

@bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل المحامي"""
    lawyer = Lawyer.query.get_or_404(id)
    cases = Case.query.filter_by(lawyer_id=id, is_active=True).order_by(Case.created_at.desc()).all()
    
    # إحصائيات المحامي
    total_cases = len(cases)
    active_cases = len([case for case in cases if case.case_status in ['pending', 'active']])
    won_cases = len([case for case in cases if case.case_status == 'won'])
    
    return render('lawyers/view.html', 
                         lawyer=lawyer, 
                         cases=cases,
                         total_cases=total_cases,
                         active_cases=active_cases,
                         won_cases=won_cases)

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """تعديل المحامي"""
    lawyer = Lawyer.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            # تحديث بيانات المستخدم
            lawyer.user.full_name = request.POST['full_name']
            lawyer.user.email = request.POST['email']
            lawyer.user.phone = request.POST.get('phone')
            
            # تحديث كلمة المرور إذا تم إدخالها
            if request.POST.get('password'):
                lawyer.user.password_hash = generate_password_hash(request.POST['password'])
            
            # تحديث بيانات المحامي
            lawyer.license_number = request.POST['license_number']
            lawyer.specialization = request.POST.get('specialization')
            lawyer.hourly_rate = request.POST.get('hourly_rate') or 0
            lawyer.bio = request.POST.get('bio')
            lawyer.updated_at = datetime.utcnow()
            
            db.save()
            
            flash('تم تحديث المحامي بنجاح', 'success')
            return redirect(url_for('lawyers.view', id=lawyer.id))
            
        except Exception as e:
            db.delete()
            flash(f'حدث خطأ أثناء تحديث المحامي: {str(e)}', 'error')
    
    return render('lawyers/edit.html', lawyer=lawyer)

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """حذف المحامي (إلغاء تفعيل)"""
    lawyer = Lawyer.query.get_or_404(id)
    
    # التحقق من وجود قضايا نشطة للمحامي
    active_cases = Case.query.filter_by(lawyer_id=id, is_active=True).count()
    
    if active_cases > 0:
        flash('لا يمكن حذف المحامي لوجود قضايا نشطة مرتبطة به', 'error')
        return redirect(url_for('lawyers.view', id=id))
    
    try:
        lawyer.is_active = False
        lawyer.user.is_active = False
        lawyer.updated_at = datetime.utcnow()
        db.save()
        
        flash('تم حذف المحامي بنجاح', 'success')
        
    except Exception as e:
        db.delete()
        flash(f'حدث خطأ أثناء حذف المحامي: {str(e)}', 'error')
    
    return redirect(url_for('lawyers.index'))

@bp.route('/api/search')
@login_required
def api_search():
    """البحث عن المحامين عبر API"""
    query = request.GET.get('q', '')
    
    if len(query) < 2:
        return JsonResponse([])
    
    lawyers = Lawyer.query.join(User).filter(
        Lawyer.is_active == True,
        User.full_name.contains(query)
    ).limit(10).all()
    
    results = []
    for lawyer in lawyers:
        results.append({
            'id': lawyer.id,
            'text': f"{lawyer.user.full_name} ({lawyer.specialization})",
            'license_number': lawyer.license_number,
            'specialization': lawyer.specialization
        })
    
    return JsonResponse(results)
